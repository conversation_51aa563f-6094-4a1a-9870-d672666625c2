{"name": "@ultraknown/disposable", "version": "1.0.0", "type": "module", "description": "", "module": "dist/index.mjs", "types": "dist/src/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist/"], "license": "MIT", "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "echo \"No tests yet\""}}