{"name": "rollup-plugin-typescript2", "version": "0.35.0", "description": "Seamless integration between Rollup and TypeScript. Now with errors.", "main": "dist/rollup-plugin-typescript2.cjs.js", "module": "dist/rollup-plugin-typescript2.es.js", "jsnext:main": "dist/rollup-plugin-typescript2.es.js", "types": "dist/index.d.ts", "files": ["dist"], "keywords": ["rollup-plugin-typescript2", "rollup-plugin-typescript", "rollup-plugin", "typescript", "es2015", "rollup", "npm"], "license": "MIT", "homepage": "https://github.com/ezolenko/rollup-plugin-typescript2", "author": "@ezolenko", "scripts": {"prebuild": "rimraf dist/*", "build": "rimraf dist/* && rollup -c", "watch": "rollup -c rollup.config.self.js -w ", "build-self": "rimraf dist/* && rollup -c rollup.config.self.js", "lint": "tslint -c ./tslint.json src/*.ts __tests__/*.ts ./*.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@rollup/pluginutils": "^4.1.2", "find-cache-dir": "^3.3.2", "fs-extra": "^10.0.0", "semver": "^7.3.7", "tslib": "^2.4.0"}, "peerDependencies": {"rollup": ">=1.26.3", "typescript": ">=2.4.0"}, "devDependencies": {"@jest/globals": "^28.0.3", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "13.2.1", "@types/find-cache-dir": "^2.0.0", "@types/fs-extra": "8.0.1", "@types/graphlib": "2.1.7", "@types/jest": "^27.5.0", "@types/lodash": "4.14.161", "@types/node": "8.0.47", "@types/object-hash": "1.3.3", "@types/semver": "7.3.12", "colors": "1.4.0", "graphlib": "2.1.8", "jest": "^28.0.0", "lodash": "4.17.21", "object-hash": "3.0.0", "rimraf": "3.0.2", "rollup": "^2.70.2", "rollup-plugin-re": "1.0.7", "rollup-plugin-typescript2": "0.34.0", "ts-jest": "^28.0.0", "tslint": "6.1.3", "typescript": "^4.6.3"}, "repository": {"type": "git", "url": "git+https://github.com/ezolenko/rollup-plugin-typescript2.git"}, "bugs": {"url": "https://github.com/ezolenko/rollup-plugin-typescript2/issues"}}